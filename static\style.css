* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.calculator {
    background: #2c3e50;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 350px;
    width: 100%;
}

.display {
    margin-bottom: 20px;
}

.display input {
    width: 100%;
    height: 80px;
    background: #34495e;
    border: none;
    border-radius: 10px;
    color: #ecf0f1;
    font-size: 2.5em;
    text-align: right;
    padding: 0 20px;
    font-weight: 300;
    letter-spacing: 1px;
}

.display input:focus {
    outline: none;
}

.buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.btn {
    height: 70px;
    border: none;
    border-radius: 15px;
    font-size: 1.4em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-number {
    background: #95a5a6;
    color: #2c3e50;
}

.btn-number:hover {
    background: #bdc3c7;
}

.btn-operator {
    background: #e67e22;
    color: white;
}

.btn-operator:hover {
    background: #f39c12;
}

.btn-equals {
    background: #27ae60;
    color: white;
    grid-column: span 2;
}

.btn-equals:hover {
    background: #2ecc71;
}

.btn-clear {
    background: #e74c3c;
    color: white;
}

.btn-clear:hover {
    background: #c0392b;
}

.btn-zero {
    grid-column: span 2;
}

.error-message {
    background: #e74c3c;
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    font-weight: 500;
    text-align: center;
    max-width: 350px;
    width: 100%;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Responsive design */
@media (max-width: 480px) {
    .calculator {
        padding: 20px;
        max-width: 300px;
    }
    
    .display input {
        height: 60px;
        font-size: 2em;
    }
    
    .btn {
        height: 60px;
        font-size: 1.2em;
    }
    
    .buttons {
        gap: 10px;
    }
}
