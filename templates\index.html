<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flask Calculator</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <div class="calculator">
            <div class="display">
                <input type="text" id="display" readonly value="0">
            </div>
            
            <div class="buttons">
                <!-- First row -->
                <button class="btn btn-clear" onclick="clearDisplay()">C</button>
                <button class="btn btn-clear" onclick="clearEntry()">CE</button>
                <button class="btn btn-operator" onclick="deleteLast()">⌫</button>
                <button class="btn btn-operator" onclick="appendToDisplay('/')" data-operator="÷">÷</button>
                
                <!-- Second row -->
                <button class="btn btn-number" onclick="appendToDisplay('7')">7</button>
                <button class="btn btn-number" onclick="appendToDisplay('8')">8</button>
                <button class="btn btn-number" onclick="appendToDisplay('9')">9</button>
                <button class="btn btn-operator" onclick="appendToDisplay('*')" data-operator="×">×</button>
                
                <!-- Third row -->
                <button class="btn btn-number" onclick="appendToDisplay('4')">4</button>
                <button class="btn btn-number" onclick="appendToDisplay('5')">5</button>
                <button class="btn btn-number" onclick="appendToDisplay('6')">6</button>
                <button class="btn btn-operator" onclick="appendToDisplay('-')">-</button>
                
                <!-- Fourth row -->
                <button class="btn btn-number" onclick="appendToDisplay('1')">1</button>
                <button class="btn btn-number" onclick="appendToDisplay('2')">2</button>
                <button class="btn btn-number" onclick="appendToDisplay('3')">3</button>
                <button class="btn btn-operator" onclick="appendToDisplay('+')">+</button>
                
                <!-- Fifth row -->
                <button class="btn btn-number btn-zero" onclick="appendToDisplay('0')">0</button>
                <button class="btn btn-number" onclick="appendToDisplay('.')">.</button>
                <button class="btn btn-equals" onclick="calculate()">=</button>
            </div>
        </div>
        
        <div class="error-message" id="errorMessage" style="display: none;"></div>
    </div>
    
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
