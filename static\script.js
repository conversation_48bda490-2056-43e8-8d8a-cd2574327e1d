let display = document.getElementById('display');
let errorMessage = document.getElementById('errorMessage');
let currentInput = '0';
let shouldResetDisplay = false;

// Initialize display
updateDisplay();

function updateDisplay() {
    display.value = currentInput;
}

function appendToDisplay(value) {
    hideError();
    
    if (shouldResetDisplay) {
        currentInput = '0';
        shouldResetDisplay = false;
    }
    
    // Handle initial zero
    if (currentInput === '0' && value !== '.') {
        if (isOperator(value)) {
            currentInput += value;
        } else {
            currentInput = value;
        }
    } else {
        // Prevent multiple consecutive operators
        if (isOperator(value) && isOperator(currentInput.slice(-1))) {
            currentInput = currentInput.slice(0, -1) + value;
        } else {
            currentInput += value;
        }
    }
    
    updateDisplay();
}

function isOperator(char) {
    return ['+', '-', '*', '/'].includes(char);
}

function clearDisplay() {
    currentInput = '0';
    shouldResetDisplay = false;
    updateDisplay();
    hideError();
}

function clearEntry() {
    // Clear the current number being entered
    let operators = ['+', '-', '*', '/'];
    let lastOperatorIndex = -1;
    
    for (let i = currentInput.length - 1; i >= 0; i--) {
        if (operators.includes(currentInput[i])) {
            lastOperatorIndex = i;
            break;
        }
    }
    
    if (lastOperatorIndex === -1) {
        currentInput = '0';
    } else {
        currentInput = currentInput.substring(0, lastOperatorIndex + 1);
    }
    
    updateDisplay();
    hideError();
}

function deleteLast() {
    if (currentInput.length > 1) {
        currentInput = currentInput.slice(0, -1);
    } else {
        currentInput = '0';
    }
    updateDisplay();
    hideError();
}

async function calculate() {
    try {
        hideError();
        
        // Don't calculate if the expression ends with an operator
        if (isOperator(currentInput.slice(-1))) {
            showError('Invalid expression');
            return;
        }
        
        const response = await fetch('/calculate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expression: currentInput
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentInput = data.result;
            shouldResetDisplay = true;
            updateDisplay();
        } else {
            showError(data.error || 'Calculation error');
        }
        
    } catch (error) {
        showError('Network error');
        console.error('Error:', error);
    }
}

function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    
    // Hide error after 3 seconds
    setTimeout(() => {
        hideError();
    }, 3000);
}

function hideError() {
    errorMessage.style.display = 'none';
}

// Keyboard support
document.addEventListener('keydown', function(event) {
    const key = event.key;
    
    // Numbers and decimal point
    if (/[0-9.]/.test(key)) {
        appendToDisplay(key);
    }
    // Operators
    else if (['+', '-', '*', '/'].includes(key)) {
        appendToDisplay(key);
    }
    // Enter or equals
    else if (key === 'Enter' || key === '=') {
        event.preventDefault();
        calculate();
    }
    // Escape or 'c' for clear
    else if (key === 'Escape' || key.toLowerCase() === 'c') {
        clearDisplay();
    }
    // Backspace for delete
    else if (key === 'Backspace') {
        event.preventDefault();
        deleteLast();
    }
});

// Prevent form submission on Enter
document.addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
    }
});
