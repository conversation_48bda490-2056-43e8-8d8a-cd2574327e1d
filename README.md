# Flask Calculator

A simple web-based calculator built with Flask for learning purposes.

## Features

- Basic arithmetic operations (+, -, ×, ÷)
- Clear and Clear Entry functions
- Delete/Backspace functionality
- Keyboard support
- Responsive design
- Error handling (division by zero, invalid expressions)
- Modern, attractive UI

## Project Structure

```
calculator_flask-learning/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── templates/
│   └── index.html        # Calculator HTML template
└── static/
    ├── style.css         # CSS styling
    └── script.js         # JavaScript functionality
```

## Installation

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the application:
   ```bash
   python app.py
   ```

3. Open your browser and navigate to:
   ```
   http://localhost:5000
   ```

## Usage

### Mouse/Touch Controls
- Click number buttons (0-9) to input numbers
- Click operator buttons (+, -, ×, ÷) for operations
- Click "=" to calculate the result
- Click "C" to clear everything
- Click "CE" to clear the current entry
- Click "⌫" to delete the last character

### Keyboard Controls
- Number keys (0-9) and decimal point (.)
- Operator keys (+, -, *, /)
- Enter or = to calculate
- Escape or C to clear
- Backspace to delete last character

## Technical Details

- **Backend**: Flask web framework
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **API**: RESTful endpoint for calculations
- **Security**: Input validation and safe expression evaluation
- **Responsive**: Works on desktop and mobile devices

## Learning Objectives

This project demonstrates:
- Flask web application structure
- RESTful API design
- Frontend-backend communication
- Input validation and security
- Responsive web design
- JavaScript event handling
- Error handling and user feedback
