from flask import Flask, render_template, request, jsonify
import re

app = Flask(__name__)

@app.route('/')
def index():
    """Render the main calculator page"""
    return render_template('index.html')

@app.route('/calculate', methods=['POST'])
def calculate():
    """Handle calculation requests"""
    try:
        data = request.get_json()
        expression = data.get('expression', '')
        
        # Basic security: only allow numbers, operators, and parentheses
        if not re.match(r'^[0-9+\-*/().\s]+$', expression):
            return jsonify({'error': 'Invalid characters in expression'}), 400
        
        # Replace common issues
        expression = expression.replace('÷', '/').replace('×', '*')
        
        # Evaluate the expression safely
        try:
            result = eval(expression)
            
            # Handle division by zero
            if str(result) == 'inf' or str(result) == '-inf':
                return jsonify({'error': 'Division by zero'}), 400
            
            # Format result to avoid floating point precision issues
            if isinstance(result, float):
                if result.is_integer():
                    result = int(result)
                else:
                    result = round(result, 10)
            
            return jsonify({'result': str(result)})
            
        except ZeroDivisionError:
            return jsonify({'error': 'Division by zero'}), 400
        except Exception as e:
            return jsonify({'error': 'Invalid expression'}), 400
            
    except Exception as e:
        return jsonify({'error': 'Server error'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
